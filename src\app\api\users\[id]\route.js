import { NextRequest, NextResponse } from "next/server";
import { getUserById, updateUser, deleteUser } from "@/lib/api/users";
import { successResponse, errorResponse, handleApiError } from "@/lib/api/response";

// GET /api/users/[id] - Get user by ID
export async function GET(request, { params }) {
  try {
    const { id } = params;
    
    if (!id) {
      return errorResponse("Missing user ID", 400);
    }

    const user = await getUserById(id);
    return successResponse(user, 200, "User retrieved successfully");
  } catch (error) {
    return handleApiError(error);
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(request, { params }) {
  try {
    const { id } = params;
    const updateData = await request.json();
    
    if (!id) {
      return errorResponse("Missing user ID", 400);
    }

    const user = await updateUser(id, updateData);
    return successResponse(user, 200, "User updated successfully");
  } catch (error) {
    return handleApiError(error);
  }
}

// DELETE /api/users/[id] - Delete user
export async function DELETE(request, { params }) {
  try {
    const { id } = params;
    
    if (!id) {
      return errorResponse("Missing user ID", 400);
    }

    const user = await deleteUser(id);
    return successResponse(user, 200, "User deleted successfully");
  } catch (error) {
    return handleApiError(error);
  }
}
