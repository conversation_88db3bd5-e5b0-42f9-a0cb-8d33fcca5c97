import { NextRequest, NextResponse } from "next/server";
import { createUser, getUserByFirebaseUid, updateUser } from "@/lib/api/users";
import { successResponse, errorResponse, handleApiError } from "@/lib/api/response";

// POST /api/users - Create a new user
export async function POST(request) {
  try {
    const userData = await request.json();
    
    // Validate required fields
    if (!userData.firebaseUid || !userData.email) {
      return errorResponse("Missing required fields: firebaseUid and email", 400);
    }

    const user = await createUser(userData);
    return successResponse(user, 201, "User created successfully");
  } catch (error) {
    return handleApiError(error);
  }
}

// GET /api/users?firebaseUid=xxx - Get user by Firebase UID
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const firebaseUid = searchParams.get("firebaseUid");

    if (!firebaseUid) {
      return errorResponse("Missing firebaseUid parameter", 400);
    }

    const user = await getUserByFirebaseUid(firebaseUid);
    return successResponse(user, 200, "User retrieved successfully");
  } catch (error) {
    return handleApiError(error);
  }
}
