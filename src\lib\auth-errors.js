/**
 * Utility functions for handling authentication errors
 */

export function getAuthErrorMessage(error) {
  if (!error) {
    return {
      message: "An unknown error occurred",
      description: "Please try again later.",
    };
  }

  // Firebase Auth errors
  if (error.code) {
    switch (error.code) {
      case 'auth/email-already-in-use':
        return {
          message: "Email already in use",
          description: "An account with this email already exists. Please try logging in instead.",
        };
      
      case 'auth/weak-password':
        return {
          message: "Password too weak",
          description: "Please choose a stronger password with at least 6 characters.",
        };
      
      case 'auth/invalid-email':
        return {
          message: "Invalid email",
          description: "Please enter a valid email address.",
        };
      
      case 'auth/user-disabled':
        return {
          message: "Account disabled",
          description: "This account has been disabled. Please contact support.",
        };
      
      case 'auth/user-not-found':
        return {
          message: "Account not found",
          description: "No account found with this email. Please check your email or create a new account.",
        };
      
      case 'auth/wrong-password':
        return {
          message: "Incorrect password",
          description: "The password you entered is incorrect. Please try again.",
        };
      
      case 'auth/too-many-requests':
        return {
          message: "Too many attempts",
          description: "Too many failed login attempts. Please try again later.",
        };
      
      case 'auth/network-request-failed':
        return {
          message: "Network error",
          description: "Please check your internet connection and try again.",
        };
      
      case 'auth/popup-closed-by-user':
        return {
          message: "Sign-in cancelled",
          description: "The sign-in process was cancelled.",
        };
      
      case 'auth/popup-blocked':
        return {
          message: "Popup blocked",
          description: "Please allow popups for this site and try again.",
        };
      
      case 'auth/cancelled-popup-request':
        return {
          message: "Sign-in cancelled",
          description: "Another sign-in popup is already open.",
        };
      
      case 'auth/operation-not-allowed':
        return {
          message: "Operation not allowed",
          description: "This sign-in method is not enabled. Please contact support.",
        };
      
      default:
        return {
          message: "Authentication failed",
          description: error.message || "Please try again later.",
        };
    }
  }

  // Database/API errors
  if (error.message) {
    if (error.message.includes('already exists')) {
      return {
        message: "Account already exists",
        description: "A user with this email already exists in our system.",
      };
    }
    
    if (error.message.includes('User not found')) {
      return {
        message: "User not found",
        description: "No user account found. Please check your credentials.",
      };
    }
    
    if (error.message.includes('Network')) {
      return {
        message: "Network error",
        description: "Please check your internet connection and try again.",
      };
    }
    
    if (error.message.includes('timeout')) {
      return {
        message: "Request timeout",
        description: "The request took too long. Please try again.",
      };
    }
  }

  // Generic fallback
  return {
    message: "Something went wrong",
    description: error.message || "Please try again later.",
  };
}

export function isNetworkError(error) {
  return (
    error?.code === 'auth/network-request-failed' ||
    error?.message?.includes('Network') ||
    error?.message?.includes('timeout') ||
    error?.name === 'NetworkError'
  );
}

export function isAuthError(error) {
  return error?.code?.startsWith('auth/');
}

export function isDatabaseError(error) {
  return (
    error?.message?.includes('Prisma') ||
    error?.message?.includes('database') ||
    error?.message?.includes('already exists') ||
    error?.message?.includes('User not found')
  );
}
