import { createContext, useContext, useEffect, useState } from "react";
import {
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  signInWithPopup,
  sendPasswordResetEmail,
  updateProfile,
} from "firebase/auth";
import { auth, googleProvider } from "@/firebase/client";
import { userApi } from "@/lib/api/client";

const AuthContext = createContext({});

export const useAuth = () => useContext(AuthContext);

export const AuthProvider = ({ children }) => {
  const [currentUser, setCurrentUser] = useState(null);
  const [dbUser, setDbUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [userLoading, setUserLoading] = useState(false);

  // Function to fetch database user data
  const fetchDbUser = async (firebaseUser) => {
    if (!firebaseUser) {
      setDbUser(null);
      return null;
    }

    try {
      setUserLoading(true);
      // Try to find user by Firebase UID
      const user = await userApi.getUserByFirebaseUid(firebaseUser.uid);
      setDbUser(user);
      return user;
    } catch (error) {
      console.error("Error fetching database user:", error);
      setDbUser(null);
      return null;
    } finally {
      setUserLoading(false);
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      setCurrentUser(user);

      if (user) {
        await fetchDbUser(user);
      } else {
        setDbUser(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  // Enhanced signup with database user creation
  const signup = async (email, password, userData = {}) => {
    try {
      setUserLoading(true);

      // Create Firebase user
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        email,
        password
      );
      const firebaseUser = userCredential.user;

      // Update Firebase profile if name is provided
      if (userData.name) {
        await updateProfile(firebaseUser, {
          displayName: userData.name,
        });
      }

      // Create database user
      const dbUserData = {
        firebaseUid: firebaseUser.uid,
        email: firebaseUser.email,
        name: userData.name || firebaseUser.displayName || null,
        imageUrl: firebaseUser.photoURL || null,
        role: userData.role || "USER",
        lastLogin: new Date(),
      };

      const newDbUser = await userApi.createUser(dbUserData);
      setDbUser(newDbUser);

      return userCredential;
    } catch (error) {
      // If database user creation fails, we should clean up the Firebase user
      if (error.message && !error.message.includes("Firebase")) {
        try {
          // Delete the Firebase user if database creation failed
          if (auth.currentUser) {
            await auth.currentUser.delete();
          }
        } catch (cleanupError) {
          console.error("Error cleaning up Firebase user:", cleanupError);
        }
      }
      throw error;
    } finally {
      setUserLoading(false);
    }
  };

  // Enhanced login with database user update
  const login = async (email, password) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const firebaseUser = userCredential.user;

      // Try to update lastLogin in database
      try {
        const dbUser = await userApi.getUserByFirebaseUid(firebaseUser.uid);
        if (dbUser) {
          await userApi.updateUser(dbUser.id, { lastLogin: new Date() });
          // Refresh the database user data
          await fetchDbUser(firebaseUser);
        }
      } catch (error) {
        console.error("Error updating last login:", error);
        // Don't fail the login if database update fails
      }

      return userCredential;
    } catch (error) {
      throw error;
    }
  };

  // Enhanced Google login with database user creation/update
  const loginWithGoogle = async () => {
    try {
      setUserLoading(true);

      const result = await signInWithPopup(auth, googleProvider);
      const firebaseUser = result.user;

      // Try to find existing database user
      let existingDbUser = null;
      try {
        existingDbUser = await userApi.getUserByFirebaseUid(firebaseUser.uid);
      } catch (error) {
        // User doesn't exist in database, we'll create them
      }

      if (!existingDbUser) {
        // Create new database user for Google sign-in
        const dbUserData = {
          firebaseUid: firebaseUser.uid,
          email: firebaseUser.email,
          name: firebaseUser.displayName || null,
          imageUrl: firebaseUser.photoURL || null,
          role: "USER",
          lastLogin: new Date(),
        };

        const newDbUser = await userApi.createUser(dbUserData);
        setDbUser(newDbUser);
      } else {
        // Update existing user's last login
        try {
          await userApi.updateUser(existingDbUser.id, {
            lastLogin: new Date(),
          });
          // Refresh the database user data
          const updatedDbUser = await userApi.getUserByFirebaseUid(
            firebaseUser.uid
          );
          setDbUser(updatedDbUser);
        } catch (error) {
          console.error("Error updating last login:", error);
          setDbUser(existingDbUser);
        }
      }

      return result;
    } catch (error) {
      throw error;
    } finally {
      setUserLoading(false);
    }
  };

  // Enhanced logout to clear all state
  const logout = async () => {
    try {
      setDbUser(null);
      return await signOut(auth);
    } catch (error) {
      throw error;
    }
  };

  // Reset password
  const resetPassword = (email) => {
    return sendPasswordResetEmail(auth, email);
  };

  const value = {
    currentUser,
    dbUser,
    loading,
    userLoading,
    signup,
    login,
    loginWithGoogle,
    logout,
    resetPassword,
    fetchDbUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};
