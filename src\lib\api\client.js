/**
 * Client-side API service for making HTTP requests to the backend API
 * This replaces direct Prisma calls from the client side
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '';

class ApiError extends Error {
  constructor(message, status, details = null) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.details = details;
  }
}

async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}/api${endpoint}`;
  
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  };

  try {
    const response = await fetch(url, config);
    const data = await response.json();

    if (!response.ok) {
      throw new ApiError(
        data.message || 'An error occurred',
        response.status,
        data.error?.details
      );
    }

    return data.data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    
    // Network or other errors
    throw new ApiError(
      'Network error occurred',
      0,
      error.message
    );
  }
}

// User API functions
export const userApi = {
  // Create a new user
  async createUser(userData) {
    return apiRequest('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // Get user by Firebase UID
  async getUserByFirebaseUid(firebaseUid) {
    return apiRequest(`/users?firebaseUid=${encodeURIComponent(firebaseUid)}`);
  },

  // Get user by ID
  async getUserById(userId) {
    return apiRequest(`/users/${userId}`);
  },

  // Update user
  async updateUser(userId, updateData) {
    return apiRequest(`/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Delete user
  async deleteUser(userId) {
    return apiRequest(`/users/${userId}`, {
      method: 'DELETE',
    });
  },
};

export { ApiError };
