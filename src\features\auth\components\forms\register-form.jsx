"use client";

import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useTransition } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/auth-context";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { registerSchema } from "@/features/auth/schema/register-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import GoogleButton from "@/features/auth/components/google-button";
import { getAuthErrorMessage } from "@/lib/auth-errors";

export function RegisterForm({ className }) {
  const [loading, startTransition] = useTransition();

  const form = useForm({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: "",
      email: "",
      password: "",
    },
  });

  const { signup, userLoading } = useAuth();
  const router = useRouter();

  const onSubmit = async (data) => {
    startTransition(async () => {
      try {
        // Use enhanced signup that creates both Firebase and database user
        await signup(data.email, data.password, {
          name: data.name,
          role: "USER",
        });

        toast.success("Account created successfully!");
        // Redirect to dashboard since user is now authenticated
        router.push("/dashboard");
      } catch (error) {
        console.error("Registration error:", error);

        let errorMessage = "Failed to create account. Please try again.";
        let errorDescription = "";

        if (error.code === "auth/email-already-in-use") {
          errorMessage = "Email already in use";
          errorDescription =
            "An account with this email already exists. Please try logging in instead.";
        } else if (error.code === "auth/weak-password") {
          errorMessage = "Password too weak";
          errorDescription = "Please choose a stronger password.";
        } else if (error.code === "auth/invalid-email") {
          errorMessage = "Invalid email";
          errorDescription = "Please enter a valid email address.";
        } else if (error.message && error.message.includes("already exists")) {
          errorMessage = "Account already exists";
          errorDescription =
            "A user with this email already exists in our system.";
        }

        toast.error(errorMessage, {
          description: errorDescription,
        });
      }
    });
  };

  return (
    <Form {...form}>
      <form
        className={cn("flex flex-col gap-6", className)}
        onSubmit={form.handleSubmit(onSubmit)}
      >
        <div className="grid gap-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter your full name"
                    disabled={loading || userLoading}
                    type="text"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Enter your email"
                    disabled={loading || userLoading}
                    type="email"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Password</FormLabel>
                <FormControl>
                  <Input
                    {...field}
                    placeholder="Create a password"
                    disabled={loading || userLoading}
                    type="password"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            className="w-full"
            disabled={loading || userLoading}
          >
            {loading || userLoading ? "Creating Account..." : "Create Account"}
          </Button>
          <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
            <span className="text-muted-foreground relative z-10 px-2">
              Or continue with
            </span>
          </div>
          <GoogleButton />
        </div>
      </form>
    </Form>
  );
}
